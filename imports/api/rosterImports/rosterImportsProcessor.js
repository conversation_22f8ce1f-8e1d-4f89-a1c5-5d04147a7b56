import _ from "lodash";
import { flatten } from "mongo-dot-notation";

import { Organizations } from "../organizations/organizations";
import { Sites } from "../sites/sites";
import { Students } from "../students/students";
import { StudentGroups } from "../studentGroups/studentGroups";
import { Users } from "../users/users";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import {
  addStudentsToCurrentClasswideActivities,
  manageStudentsScores,
  removeIndividualInterventionsFromActiveAssessments,
  removeStudentsFromActiveAssessments
} from "./manageStudentsScores";
// eslint-disable-next-line import/no-cycle
import { getIdsOfStudentsInClasswideIntervention, getStudentsWithIndividualInterventionInProgress } from "./methods";
import { createCurrentBenchmarkWindowForSite } from "../benchmarkWindows/methods";
import { removeActiveBenchmarks } from "./removeActiveBenchmarks";
import { createEmptySkillGroupsForSite } from "../studentsBySkill/methods";
import { insertUser } from "../users/server/methods";
import { getCurrentSchoolYear, getMeteorUser } from "../utilities/utilities";
import { insertSite } from "./helpers";
import { isExternalRostering } from "../../ui/utilities";

export default class RosterImportsProcessor {
  constructor({
    items,
    orgid,
    byDateOn,
    rosterImportId,
    userId,
    districtName,
    allowMultipleGradeLevels,
    organizationCoachesEmails,
    rostering,
    schoolYear,
    districtID
  }) {
    this.items = items;
    this.orgid = orgid;
    this.byDateOn = byDateOn;
    this.rosterImportId = rosterImportId;
    this.userId = userId;
    this.districtName = districtName;
    this.allowMultipleGradeLevels = allowMultipleGradeLevels;
    this.organizationCoachesEmails = organizationCoachesEmails;

    this.schoolYear = schoolYear;
    this.districtID = districtID;

    this.isExternalRostering = isExternalRostering(rostering);
    this.districtName = districtName;
    this.allowMultipleGradeLevels = allowMultipleGradeLevels;
    this.studentIdsByGroupIdsForHistoryCleanup = {
      classwide: {},
      individual: {},
      other: {}
    };
    this.idsOfGroupsThatChangedGrade = [];
    this.idsOfStudentsInClasswideIntervention = [];
    this.idsOfStudentsInIndividualIntervention = [];
  }

  static async init({ items, orgid, byDateOn, rosterImportId, userId }) {
    const { name: districtName, allowMultipleGradeLevels = false, rostering } = await Organizations.findOneAsync(orgid);

    const organizationCoachesEmails = (
      await Users.find(
        {
          "profile.orgid": orgid,
          "profile.siteAccess.role": "arbitraryIdadmin"
        },
        {
          fields: {
            emails: 1
          }
        }
      ).fetchAsync()
    ).map(coach => _.get(coach, "emails[0].address", ""));

    // Get site information for districtID
    const site = await Sites.findOneAsync({ orgid }, { fields: { "stateInformation.districtNumber": 1 } });
    const defaultDistrictID = site?.stateInformation?.districtNumber;
    const { schoolYear, districtID } = items[0]?.data || {};
    const finalSchoolYear = schoolYear || getCurrentSchoolYear(getMeteorUser(), orgid);
    const finalDistrictID = districtID || defaultDistrictID;

    return new RosterImportsProcessor({
      items,
      orgid,
      byDateOn,
      rosterImportId,
      userId,
      districtName,
      allowMultipleGradeLevels,
      organizationCoachesEmails,
      rostering,
      schoolYear: finalSchoolYear,
      districtID: finalDistrictID
    });
  }

  highSchoolGrades = ["09", "10", "11", "12", "HS"];

  getStudentGroupGroupingKey(studentGroupName, grade) {
    return this.allowMultipleGradeLevels ? studentGroupName : `${studentGroupName}_${grade}`;
  }

  async process() {
    if (!this.schoolYear) {
      return;
    }
    this.idsOfStudentsInClasswideIntervention = await getIdsOfStudentsInClasswideIntervention(this.orgid);
    this.idsOfStudentsInIndividualIntervention = (
      await getStudentsWithIndividualInterventionInProgress({
        orgid: this.orgid
      })
    ).map(student => student._id);

    const sitesBySchoolId = {};
    const teachersByLocalId = {};
    const studentsByLocalId = {};
    const studentGroupsBySchoolAndGroupName = {};
    const studentGroupEnrollments = [];
    const activeStudentGroupIds = [];

    this.items.forEach(item => {
      const schoolId = item.data.schoolID;
      const teacherLocalId = item.data.teacherID;
      const studentLocalId = item.data.studentLocalID;

      if (!teachersByLocalId[teacherLocalId]) {
        teachersByLocalId[teacherLocalId] = {
          firstName: item.data.teacherFirstName,
          lastName: item.data.teacherLastName,
          email: item.data.teacherEmail,
          schoolIds: new Set([schoolId])
        };
      } else {
        teachersByLocalId[teacherLocalId].schoolIds.add(schoolId);
      }

      if (!sitesBySchoolId[schoolId]) {
        sitesBySchoolId[schoolId] = {
          id: schoolId,
          name: item.data.schoolName,
          grades: new Set([item.data.springMathGrade])
        };
      } else {
        sitesBySchoolId[schoolId].grades.add(item.data.springMathGrade);
      }

      if (!studentsByLocalId[studentLocalId]) {
        studentsByLocalId[studentLocalId] = {
          studentGrade: item.data.studentGrade,
          grade: item.data.springMathGrade,
          demographic: {
            birthDate: item.data.studentBirthDate && item.data.studentBirthDate.toISOString().substr(0, 10),
            birthDateTimeStamp: item.data.studentBirthDate && item.data.studentBirthDate.getTime()
          },
          identity: {
            name: {
              firstName: item.data.studentFirstName,
              lastName: item.data.studentLastName
            },
            identification: {
              localId: studentLocalId,
              stateId: item.data.studentStateID
            }
          }
        };
      }

      if (!studentGroupsBySchoolAndGroupName[schoolId]) {
        studentGroupsBySchoolAndGroupName[schoolId] = {};
      }

      const studentGroupGroupingKey = this.getStudentGroupGroupingKey(item.data.courseName, item.data.springMathGrade);
      if (!studentGroupsBySchoolAndGroupName[schoolId][studentGroupGroupingKey]) {
        studentGroupsBySchoolAndGroupName[schoolId][studentGroupGroupingKey] = {
          name: item.data.courseName,
          grade: item.data.springMathGrade,
          sectionId: item.data.classSectionID,
          teacherLocalId,
          schoolId,
          secondaryTeachersLocalIds: []
        };
      } else if (
        ![
          studentGroupsBySchoolAndGroupName[schoolId][studentGroupGroupingKey].teacherLocalId,
          ...studentGroupsBySchoolAndGroupName[schoolId][studentGroupGroupingKey].secondaryTeachersLocalIds
        ].includes(teacherLocalId)
      ) {
        studentGroupsBySchoolAndGroupName[schoolId][studentGroupGroupingKey].secondaryTeachersLocalIds.push(
          teacherLocalId
        );
      }

      studentGroupEnrollments.push({
        grade: item.data.springMathGrade,
        studentGroupName: item.data.courseName,
        studentLocalId,
        schoolId
      });
    });

    const siteIdBySchoolId = {};

    // eslint-disable-next-line no-restricted-syntax
    for await (const site of Object.values(sitesBySchoolId)) {
      siteIdBySchoolId[site.id] = await this.processSite(site, this.userId);
    }

    // eslint-disable-next-line no-restricted-syntax
    for await (const [teacherLocalId, teacher] of Object.entries(teachersByLocalId)) {
      const schoolIds = Array.from(teacher.schoolIds);
      const siteIds = schoolIds.map(schoolId => siteIdBySchoolId[schoolId]);
      teachersByLocalId[teacherLocalId].id = await this.processUser({
        teacherLocalId,
        siteIds,
        teacher,
        userId: this.userId
      });
    }

    // eslint-disable-next-line no-restricted-syntax
    for await (const student of Object.values(studentsByLocalId)) {
      const studentLocalId = student.identity.identification.localId;
      studentsByLocalId[studentLocalId].id = await this.processStudent(student);
    }

    // eslint-disable-next-line no-restricted-syntax
    for await (const schoolGroups of Object.values(studentGroupsBySchoolAndGroupName)) {
      // eslint-disable-next-line no-restricted-syntax
      for await (const studentGroup of Object.values(schoolGroups)) {
        const siteId = siteIdBySchoolId[studentGroup.schoolId];
        const teacherId = teachersByLocalId[studentGroup.teacherLocalId].id;
        const studentGroupId = await this.processStudentGroup({
          studentGroup,
          siteId,
          teacherId,
          secondaryTeachers: studentGroup.secondaryTeachersLocalIds.map(localId => teachersByLocalId[localId].id)
        });
        studentGroupsBySchoolAndGroupName[studentGroup.schoolId][
          this.getStudentGroupGroupingKey(studentGroup.name, studentGroup.grade)
        ].id = studentGroupId;
        activeStudentGroupIds.push(studentGroupId);
      }
    }

    // eslint-disable-next-line no-restricted-syntax
    for await (const studentGroupEnrollment of studentGroupEnrollments) {
      const siteId = siteIdBySchoolId[studentGroupEnrollment.schoolId];
      const studentId = studentsByLocalId[studentGroupEnrollment.studentLocalId].id;
      const studentGroupId =
        studentGroupsBySchoolAndGroupName[studentGroupEnrollment.schoolId][
          this.getStudentGroupGroupingKey(studentGroupEnrollment.studentGroupName, studentGroupEnrollment.grade)
        ].id;
      await this.processStudentGroupEnrollment({
        studentId,
        studentGroupEnrollment,
        siteId,
        studentGroupId
      });
    }

    await this.deactivateStudentGroups(activeStudentGroupIds);
    const activeStudentIds = _.map(studentsByLocalId, "id");
    await this.deactivateStudentGroupEnrollments(activeStudentIds);
    if (this.hasHistoryToProcess()) {
      await manageStudentsScores(this.studentIdsByGroupIdsForHistoryCleanup, this.orgid);
    }
    if (this.idsOfGroupsThatChangedGrade.length) {
      await removeActiveBenchmarks(this.idsOfGroupsThatChangedGrade);
    }
  }

  async processUploadedStudents(siteId, studentGroupId) {
    const studentsByLocalId = {};
    const studentGroupEnrollments = [];

    this.items.forEach(item => {
      const schoolId = item.data.schoolID;
      const studentLocalId = item.data.studentLocalID;

      if (!studentsByLocalId[studentLocalId]) {
        studentsByLocalId[studentLocalId] = {
          studentGrade: item.data.studentGrade,
          grade: item.data.springMathGrade,
          demographic: {
            birthDate: item.data.studentBirthDate && item.data.studentBirthDate.toISOString().substr(0, 10),
            birthDateTimeStamp: item.data.studentBirthDate && item.data.studentBirthDate.getTime()
          },
          identity: {
            name: {
              firstName: item.data.studentFirstName,
              lastName: item.data.studentLastName
            },
            identification: {
              localId: studentLocalId,
              stateId: item.data.studentStateID
            }
          }
        };
      }

      studentGroupEnrollments.push({
        grade: item.data.springMathGrade,
        studentGroupName: item.data.courseName,
        studentLocalId,
        schoolId
      });
    });

    const newStudentsIds = [];
    // eslint-disable-next-line no-restricted-syntax
    for await (const student of Object.values(studentsByLocalId)) {
      const studentLocalId = student.identity.identification.localId;
      studentsByLocalId[studentLocalId].id = await this.processStudent(student);
      newStudentsIds.push(studentsByLocalId[studentLocalId].id);
    }

    // eslint-disable-next-line no-restricted-syntax
    for await (const studentGroupEnrollment of studentGroupEnrollments) {
      const studentId = studentsByLocalId[studentGroupEnrollment.studentLocalId].id;
      await this.processStudentGroupEnrollment({
        studentId,
        studentGroupEnrollment,
        siteId,
        studentGroupId
      });
    }

    const studentGroup = await StudentGroups.findOneAsync(studentGroupId);
    await addStudentsToCurrentClasswideActivities({
      currentGroup: studentGroup,
      newStudentsIds
    });
  }

  async processUser({ teacherLocalId, siteIds, teacher, userId }) {
    const role = this.organizationCoachesEmails.includes(teacher.email) ? "arbitraryIdadmin" : "arbitraryIdteacher";
    const newUser = {
      orgid: this.orgid,
      localId: teacherLocalId,
      districtNumber: this.districtID,
      role,
      siteIds,
      schoolYear: this.schoolYear,
      isActive: true,
      name: {
        first: teacher.firstName,
        last: teacher.lastName
      },
      email: teacher.email,
      created: this.byDateOn,
      lastModified: this.byDateOn,
      rosterImportId: this.rosterImportId
    };
    return insertUser(newUser, userId);
  }

  async processSite(site, userId) {
    const siteDocument = await Sites.findOneAsync({
      orgid: this.orgid,
      "stateInformation.schoolNumber": site.id
    });

    // eslint-disable-next-line no-param-reassign
    site.isHighSchool = [...site.grades].every(grade => this.highSchoolGrades.includes(grade));

    if (!siteDocument) {
      const siteId = await insertSite({
        orgid: this.orgid,
        schoolYear: this.schoolYear,
        districtId: this.districtID,
        districtName: this.districtName,
        schoolNumber: site.id,
        name: site.name,
        grades: site.grades,
        isHighSchool: site.isHighSchool,
        byDateOn: this.byDateOn,
        rosterImportId: this.rosterImportId
      });
      createCurrentBenchmarkWindowForSite({ orgid: this.orgid, siteId, userId });
      createEmptySkillGroupsForSite({ siteId });
      return siteId;
    }

    await this.updateSite(site, siteDocument);

    return siteDocument._id;
  }

  async updateSite(site, siteDocument) {
    const newDocument = {
      schoolYear: this.schoolYear,
      stateInformation: {
        districtName: this.districtName
      },
      ...(this.isExternalRostering ? { name: site.name } : {}),
      grades: Array.from(site.grades),
      isHighSchool: site.isHighSchool
    };

    const isNewDocumentEqual = _.isEqual(
      _.pick(siteDocument, ["schoolYear", "stateInformation.districtName", "grades", "isHighSchool"]),
      newDocument
    );

    if (!isNewDocumentEqual) {
      await Sites.updateAsync(
        { _id: siteDocument._id },
        this.getSetToUpdateDocumentAndLastModifiedData(newDocument, {
          isVisible: true
        })
      );
    }
  }

  getSetToUpdateDocumentAndLastModifiedData(updatedDocument, otherOptionsToSet) {
    const setObject = {
      ...flatten(updatedDocument).$set,
      lastModified: this.byDateOn,
      rosterImportId: this.rosterImportId,
      ...otherOptionsToSet
    };
    const unsetObject = {};
    if (updatedDocument.demographic && !updatedDocument.demographic?.birthDate) {
      unsetObject["demographic.birthDate"] = "";
      unsetObject["demographic.birthDateTimeStamp"] = "";
      delete setObject["demographic.birthDate"];
      delete setObject["demographic.birthDateTimeStamp"];
    }

    return {
      $set: setObject,
      ...(Object.keys(unsetObject).length ? { $unset: unsetObject } : {})
    };
  }

  async processStudent(student) {
    const studentDocument = await Students.findOneAsync({
      orgid: this.orgid,
      "identity.identification.localId": student.identity.identification.localId,
      "identity.identification.stateId": student.identity.identification.stateId,
      schoolYear: this.schoolYear
    });

    if (!studentDocument) {
      return this.insertStudent(student);
    }

    await this.updateStudent(student, studentDocument);

    return studentDocument._id;
  }

  async insertStudent(student) {
    const newStudent = {
      orgid: this.orgid,
      schoolYear: this.schoolYear,
      districtNumber: this.districtID,
      created: this.byDateOn,
      lastModified: this.byDateOn,
      rosterImportId: this.rosterImportId,
      ...student
    };

    return Students.insertAsync(newStudent);
  }

  async updateStudent(student, studentDocument) {
    const newDocument = {
      schoolYear: this.schoolYear,
      // NOTE - prevent updating students that do not have student grade
      ...(student.studentGrade && { studentGrade: student.studentGrade }),
      grade: student.grade,
      demographic: {
        birthDate: student.demographic.birthDate,
        birthDateTimeStamp: student.demographic.birthDateTimeStamp
      },
      identity: {
        name: {
          firstName: student.identity.name.firstName,
          lastName: student.identity.name.lastName
        }
      }
    };

    const isNewDocumentEqual = _.isEqual(
      _.pick(studentDocument, [
        "schoolYear",
        "grade",
        "studentGrade",
        "demographic.birthDate",
        "demographic.birthDateTimeStamp",
        "identity.name.firstName",
        "identity.name.lastName"
      ]),
      newDocument
    );

    if (!isNewDocumentEqual) {
      await Students.updateAsync(
        { _id: studentDocument._id },
        this.getSetToUpdateDocumentAndLastModifiedData(newDocument)
      );
    }
  }

  async processStudentGroup({ studentGroup, siteId, teacherId, secondaryTeachers = [] }) {
    const studentGroupQuery = {
      orgid: this.orgid,
      schoolYear: this.schoolYear,
      sectionId: studentGroup.sectionId,
      grade: studentGroup.grade,
      siteId,
      isActive: true
    };
    let studentGroupDocument = await StudentGroups.findOneAsync(studentGroupQuery);

    let ownerId = teacherId;
    if (!ownerId) {
      const teacher = await Users.findOneAsync({
        "profile.orgid": this.orgid,
        "profile.localId": studentGroup.teacherLocalId
      });

      ownerId = teacher ? teacher._id : "";
    }

    if (!studentGroupDocument) {
      // Attempting to find inactive matching student group before creating a new one
      studentGroupDocument = await StudentGroups.findOneAsync({ ...studentGroupQuery, isActive: false });
      if (!studentGroupDocument) {
        return this.insertStudentGroup(siteId, ownerId, studentGroup, secondaryTeachers);
      }
    }

    await this.updateStudentGroup(studentGroup, ownerId, studentGroupDocument, secondaryTeachers);

    return studentGroupDocument._id;
  }

  async insertStudentGroup(siteId, ownerId, studentGroup, secondaryTeachers = []) {
    const newStudentGroup = {
      orgid: this.orgid,
      rosterImportId: this.rosterImportId,
      type: "CLASS",
      schoolYear: this.schoolYear,
      created: this.byDateOn,
      lastModified: this.byDateOn,
      isActive: true,
      siteId,
      ownerIds: [ownerId],
      name: studentGroup.name,
      grade: studentGroup.grade,
      sectionId: studentGroup.sectionId,
      secondaryTeachers
    };

    return StudentGroups.insertAsync(newStudentGroup);
  }

  async updateStudentGroup(studentGroup, ownerId, studentGroupDocument, secondaryTeachers = []) {
    const newDocument = {
      name: studentGroup.name,
      grade: studentGroup.grade,
      ownerIds: [ownerId],
      secondaryTeachers,
      isActive: true
    };

    const isNewDocumentEqual = _.isEqual(
      _.pick(studentGroupDocument, ["name", "grade", "ownerIds", "isActive", "secondaryTeachers"]),
      newDocument
    );

    if (!isNewDocumentEqual) {
      await StudentGroups.updateAsync(
        { _id: studentGroupDocument._id },
        this.getSetToUpdateDocumentAndLastModifiedData(newDocument)
      );
      const hasGradeChanged = newDocument.grade !== studentGroupDocument.grade;
      if (hasGradeChanged) {
        this.idsOfGroupsThatChangedGrade.push(studentGroupDocument._id);
      }
    }
  }

  async processStudentGroupEnrollment({ studentId, studentGroupEnrollment, siteId, studentGroupId }) {
    const studentGroupEnrollmentDocument = await StudentGroupEnrollments.findOneAsync({
      orgid: this.orgid,
      siteId,
      studentGroupId,
      studentId,
      isActive: true,
      schoolYear: this.schoolYear
    });

    if (!studentGroupEnrollmentDocument) {
      return this.insertStudentGroupEnrollment(siteId, studentGroupEnrollment, studentGroupId, studentId);
    }

    await this.updateStudentGroupEnrollment(studentGroupEnrollment, studentGroupEnrollmentDocument);

    return studentGroupEnrollmentDocument._id;
  }

  async insertStudentGroupEnrollment(siteId, studentGroupEnrollment, studentGroupId, studentId) {
    const newStudentGroupEnrollment = {
      orgid: this.orgid,
      rosterImportId: this.rosterImportId,
      siteId,
      grade: studentGroupEnrollment.grade,
      studentGroupId,
      studentId,
      isActive: true,
      schoolYear: this.schoolYear,
      giftedAndTalented: studentGroupEnrollment.giftedAndTalented,
      ELL: studentGroupEnrollment.ELL,
      IEP: null,
      title1: studentGroupEnrollment.title1,
      freeReducedLunch: studentGroupEnrollment.freeReducedLunch,
      created: this.byDateOn,
      lastModified: this.byDateOn
    };

    await this.deactivateStudentGroupEnrollment(studentId);

    return StudentGroupEnrollments.insertAsync(newStudentGroupEnrollment);
  }

  async updateStudentGroupEnrollment(studentGroupEnrollment, studentGroupEnrollmentDocument) {
    const newDocument = {
      grade: studentGroupEnrollment.grade,
      giftedAndTalented: studentGroupEnrollment.giftedAndTalented,
      ELL: studentGroupEnrollment.ELL,
      title1: studentGroupEnrollment.title1,
      freeReducedLunch: studentGroupEnrollment.freeReducedLunch
    };

    const isNewDocumentEqual = _.isEqual(
      _.pick(studentGroupEnrollmentDocument, ["grade", "giftedAndTalented", "ELL", "title1", "freeReducedLunch"]),
      newDocument
    );

    if (!isNewDocumentEqual) {
      await StudentGroupEnrollments.updateAsync(
        { _id: studentGroupEnrollmentDocument._id },
        this.getSetToUpdateDocumentAndLastModifiedData(newDocument)
      );
    }
  }

  async deactivateStudentGroups(activeStudentGroupIds) {
    await StudentGroups.updateAsync(
      {
        orgid: this.orgid,
        schoolYear: this.schoolYear,
        isActive: true,
        _id: { $nin: activeStudentGroupIds }
      },
      {
        $set: {
          isActive: false,
          lastModified: this.byDateOn,
          rosterImportId: this.rosterImportId
        }
      },
      { multi: true }
    );
  }

  async deactivateStudentGroupEnrollments(activeStudentIds) {
    await this.deactivateStudentGroupEnrollment({ $nin: activeStudentIds });
  }

  async deactivateStudentGroupEnrollment(studentIdQuery) {
    const query = {
      orgid: this.orgid,
      schoolYear: this.schoolYear,
      isActive: true,
      studentId: studentIdQuery
    };
    if (typeof studentIdQuery === "string") {
      await this.markStudentsInActiveAssessments(query);
    } else {
      const removedStudents = await StudentGroupEnrollments.find(query, {
        fields: { studentId: 1, studentGroupId: 1 }
      }).fetchAsync();
      await removeIndividualInterventionsFromActiveAssessments(removedStudents);
      await removeStudentsFromActiveAssessments(removedStudents);
    }
    await StudentGroupEnrollments.updateAsync(
      query,
      {
        $set: {
          isActive: false,
          lastModified: this.byDateOn,
          rosterImportId: this.rosterImportId
        }
      },
      { multi: true }
    );
  }

  async markStudentsInActiveAssessments(query) {
    const arraysToQuery = ["idsOfStudentsInClasswideIntervention", "idsOfStudentsInIndividualIntervention"];
    const activeStudentGroupEnrollment = await StudentGroupEnrollments.findOneAsync(query);
    const previousStudentGroupId = activeStudentGroupEnrollment
      ? activeStudentGroupEnrollment.studentGroupId
      : "unenrolled";
    const isStudentInActiveAssignment = arraysToQuery.some(array => this[array].includes(query.studentId));
    if (!activeStudentGroupEnrollment || !isStudentInActiveAssignment) {
      this.markStudentsByActiveAssessment(previousStudentGroupId, query.studentId);
    } else {
      arraysToQuery.forEach(arrayToQuery => {
        this.groupStudentsByActiveAssessment(arrayToQuery, query.studentId, previousStudentGroupId);
      });
    }
  }

  markStudentsByActiveAssessment(previousStudentGroupId, studentId, interventionType = "other") {
    if (!this.studentIdsByGroupIdsForHistoryCleanup[interventionType][previousStudentGroupId]) {
      this.studentIdsByGroupIdsForHistoryCleanup[interventionType][previousStudentGroupId] = [];
    }
    this.studentIdsByGroupIdsForHistoryCleanup[interventionType][previousStudentGroupId].push(studentId);
  }

  groupStudentsByActiveAssessment(arrayOfStudentsInIntervention, studentId, previousStudentGroupId) {
    if (!this[arrayOfStudentsInIntervention].includes(studentId)) return;
    const interventionType =
      arrayOfStudentsInIntervention === "idsOfStudentsInClasswideIntervention" ? "classwide" : "individual";
    this.markStudentsByActiveAssessment(previousStudentGroupId, studentId, interventionType);
  }

  hasHistoryToProcess() {
    return (
      Object.keys(this.studentIdsByGroupIdsForHistoryCleanup.classwide).length > 0 ||
      Object.keys(this.studentIdsByGroupIdsForHistoryCleanup.individual).length > 0 ||
      Object.keys(this.studentIdsByGroupIdsForHistoryCleanup.other).length > 0
    );
  }
}
