import React, { Component } from "react";
import PropTypes from "prop-types";

import Highcharts from "highcharts/highstock";
import HighchartsReact from "highcharts-react-official";
import get from "lodash/get";
import {
  dayInMilliseconds,
  getPlotLine,
  getTooltipTimeLabelFormats,
  getXAxisDateTimeLabelFormats,
  monthInMilliseconds,
  weekInMilliseconds
} from "/imports/ui/utilities";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";
import { getCurrentDate } from "/imports/api/helpers/getCurrentDate";
import { isEqual } from "lodash";

class ClasswidePMChart extends Component {
  constructor(props) {
    super(props);
    this.state = {
      chartOptions: this.getChartOptions()
    };
  }

  componentDidUpdate(prevProps) {
    if (
      !isEqual(prevProps.scores, this.props.scores) ||
      prevProps.shouldShowStudentsScores !== this.props.shouldShowStudentsScores ||
      prevProps.selectedScoreDate !== this.props.selectedScoreDate
    ) {
      this.setState({ chartOptions: this.getChartOptions() });
    }
  }

  getTargetSeries = dataSeries => {
    const customDate = get(getMeteorUserSync(), "profile.customDate");
    const startingPointTimestamp = get(
      dataSeries,
      "[0].data[0][0]",
      new Date(getCurrentDate(customDate, this.props.orgid)).getTime()
    );
    return {
      name: "",
      type: "scatter",
      marker: {
        enabled: false
      },
      showInLegend: false,
      data: [
        [startingPointTimestamp, this.props.scores.instructionalTarget],
        [startingPointTimestamp, this.props.scores.masteryTarget]
      ],
      enableMouseTracking: false
    };
  };

  getAdditionalXAxisConfiguration = dataSeries => {
    const configuration = {
      startOnTick: true
    };
    const scoresSeriesNumber = 1;
    const numberOfScores = get(dataSeries, `[${scoresSeriesNumber}].data.length`, 0);
    if (numberOfScores > 0) {
      const classWideMedian = dataSeries.find(d => d.name === "Classwide Median");
      const classWideMedianData = classWideMedian ? classWideMedian.data : dataSeries[1].data;
      const firstScoreTimestamp = classWideMedianData[0][0] ? classWideMedianData[0][0] : classWideMedianData[0].x;
      const lastScoreTimestamp = classWideMedianData[numberOfScores - 1][0]
        ? classWideMedianData[numberOfScores - 1][0]
        : classWideMedianData[numberOfScores - 1].x;
      const firstAndLastDataPointTimeDifference = lastScoreTimestamp - firstScoreTimestamp;

      const { timeRange } = this.props;
      if (timeRange) {
        const startEndTimeDifference = timeRange.end - timeRange.start;
        configuration.min = timeRange.start;
        configuration.max =
          startEndTimeDifference < monthInMilliseconds
            ? firstScoreTimestamp + monthInMilliseconds
            : timeRange.end + dayInMilliseconds;
      } else {
        configuration.min = firstScoreTimestamp;
        configuration.max =
          firstAndLastDataPointTimeDifference < monthInMilliseconds
            ? firstScoreTimestamp + monthInMilliseconds
            : lastScoreTimestamp + weekInMilliseconds;
      }
      // eslint-disable-next-line no-param-reassign
      dataSeries[0].data[0][0] = firstScoreTimestamp;
      // eslint-disable-next-line no-param-reassign
      dataSeries[0].data[1][0] = lastScoreTimestamp;

      if (firstAndLastDataPointTimeDifference < monthInMilliseconds) {
        configuration.endOnTick = true;
      }
    } else {
      configuration.minRange = monthInMilliseconds;
    }
    return configuration;
  };

  selectLatestMedianScorePointOnInitialLoad = dataSeries => {
    if (this.props.displayInterventionScores) {
      this.selectLatestMedianScorePointOnInitialLoad = () => {};
    }
    if (this.props.selectedScoreDate) {
      const cwMedianData = dataSeries.find(d => d.name === "Classwide Median").data;
      cwMedianData[cwMedianData.length - 1].selected = true;
      this.props.showSelectedScores({
        preventDefault: () => {},
        target: { x: cwMedianData[cwMedianData.length - 1].x }
      });
    }
  };

  getChartOptions() {
    const options = this.props.options || {
      chartType: "line",
      title: "",
      height: 400,
      xAxisTitle: "",
      yAxisTitle: "Score",
      marginTop: 50,
      marginRight: 50
    };
    const chartOptions = {
      height: options.height || "",
      type: options.chartType || "line",
      marginRight: options.marginRight || 50,
      marginTop: options.marginTop || 20,
      zoomType: "xy",
      zooming: {
        mouseWheel: false
      }
    };
    const { scores } = this.props;
    const isInClasswideView = scores.allStudentsScores && scores.allStudentsScores.length > 0;
    if (isInClasswideView) {
      chartOptions.spacingRight = 0; // to minimize the effect of covering part of the chart with legend
    }

    const classroomSeries = {
      data: scores.classMedianScores,
      animation: {
        duration: 0
      },
      name: "Classwide Median",
      color: "rgba(255, 0, 0, 0.7)",
      lineWidth: 8,
      marker: {
        enabled: true,
        symbol: "circle",
        radius: 7,
        color: "rgba(255, 0, 0, 1)"
      }
    };

    const studentSeries = {
      data: scores.studentScores,
      name: this.props.studentName,
      color: "#0961CD",
      lineWidth: 2,
      marker: {
        enabled: true,
        symbol: "circle",
        radius: 5,
        color: "#0961CD"
      }
    };

    let dataSeries = [];

    // all graphs should use this
    if (scores.classMedianScores && scores.classMedianScores.length > 0) {
      classroomSeries.data = classroomSeries.data.map(data => {
        return {
          x: data[0],
          y: data[1],
          selected: data[0] === this.props.selectedScoreDate,
          ...(data[2] ? { name: "Preview" } : {})
        };
      });
      dataSeries.push(classroomSeries);
    }
    // individual detail view
    if (scores.studentScores && scores.studentScores.length > 0) {
      dataSeries.push(studentSeries);
    }
    // classwide detail view
    if (isInClasswideView) {
      // filter scores without student's name
      scores.allStudentsScores = scores.allStudentsScores.filter(
        score => !!score.lastName && (!this.props.studentName || score.name === this.props.studentName)
      );
      const hideableStudentScores = scores.allStudentsScores.map(score => {
        const scoreCopy = { ...score };
        scoreCopy.visible = this.props.shouldShowStudentsScores;
        scoreCopy.data = score.data.map(([x, y, name]) => ({ x, y, ...(name ? { name: "Preview" } : {}) }));
        return scoreCopy;
      });
      dataSeries = [...dataSeries, ...hideableStudentScores];
    }
    const targetsSeries = this.getTargetSeries(dataSeries);
    dataSeries.unshift(targetsSeries);

    if (!this.props.displayInterventionScores) {
      const classwideMedian = dataSeries.find(d => d.name === "Classwide Median");
      if (classwideMedian) {
        // eslint-disable-next-line no-return-assign,no-param-reassign
        classwideMedian.data.forEach(dp => (dp.selected = false));
      }
    }

    if (this.props.isSuperAdminOrUniversalDataAdminOrDataAdmin) {
      this.selectLatestMedianScorePointOnInitialLoad(dataSeries);
    }

    const { showSelectedScores, hideSelectedScores } = this.props;

    return {
      credits: {
        enabled: false
      },
      chart: {
        ...chartOptions,
        events: {
          click: event => {
            hideSelectedScores?.(event);
          }
        }
      },
      accessibility: {
        enabled: false
      },
      title: {
        text: options.title || "",
        x: -50
      },
      legend: {
        layout: "vertical",
        align: "right",
        verticalAlign: "top",
        navigation: {
          activeColor: "#3E576F",
          animation: true,
          arrowSize: 20,
          inactiveColor: "#CCC",
          style: {
            fontWeight: "bold",
            color: "#333",
            fontSize: "18px"
          }
        },
        itemHiddenStyle: {
          color: "#ccc",
          textDecoration: "none"
        }
      },
      plotOptions: {
        series: {
          connectNulls: true,
          animation: false,
          allowPointSelect: !!showSelectedScores && this.props.scoresClickable,
          point: {
            events: {
              select(e) {
                if (this.name !== "Preview") {
                  showSelectedScores(e);
                }
              },
              unselect: hideSelectedScores
            }
          }
        },
        line: {
          states: {
            hover: {
              lineWidthPlus: 4
            }
          }
        }
      },
      series: dataSeries,
      tooltip: {
        dateTimeLabelFormats: getTooltipTimeLabelFormats(),
        split: false
      },
      xAxis: {
        type: "datetime",
        dateTimeLabelFormats: getXAxisDateTimeLabelFormats(),
        ordinal: false,
        minPadding: 0.08,
        maxPadding: 0.08,
        minTickInterval: weekInMilliseconds,
        title: {
          text: options.xAxisTitle || ""
        },
        minorTickLength: 0,
        tickLength: 5,
        crosshair: {
          enabled: true
        },
        ...this.getAdditionalXAxisConfiguration(dataSeries)
      },
      yAxis: {
        title: {
          text: options.yAxisTitle || ""
        },
        gridLineDashStyle: "Dot",
        gridLineColor: "#95A0A6",
        plotLines: [
          getPlotLine(this.props.scores.masteryTarget, "Mastery"),
          getPlotLine(this.props.scores.instructionalTarget, "Instructional")
        ]
      }
    };
  }

  render() {
    return (
      <div
        id={this.props.chartId}
        className="student-detail-pm-chart"
        data-testid="classwide-pm-chart"
        data-mastery-target={this.props.scores.masteryTarget}
        data-instructional-target={this.props.scores.instructionalTarget}
      >
        <HighchartsReact highcharts={Highcharts} options={this.state.chartOptions} />
      </div>
    );
  }
}

ClasswidePMChart.propTypes = {
  chartId: PropTypes.string,
  scores: PropTypes.object,
  studentName: PropTypes.string,
  options: PropTypes.object,
  type: PropTypes.string,
  shouldShowStudentsScores: PropTypes.bool,
  selectedScoreDate: PropTypes.number,
  showSelectedScores: PropTypes.func,
  hideSelectedScores: PropTypes.func,
  scoresClickable: PropTypes.bool,
  timeRange: PropTypes.object,
  location: PropTypes.object,
  currentAssessmentClasswideSkill: PropTypes.object,
  displayInterventionScores: PropTypes.bool,
  isSuperAdminOrUniversalDataAdminOrDataAdmin: PropTypes.bool,
  orgid: PropTypes.string
};

ClasswidePMChart.defaultProps = {
  scoresClickable: false
};

export default ClasswidePMChart;
