import React, { Component, useContext } from "react";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import PropTypes from "prop-types";
import { get, groupBy } from "lodash";

import Header from "/imports/ui/components/admin-view/admin-header";
import AdminScreeningResults from "/imports/ui/components/admin-view/admin-screening-results";
import AdminSchoolwideProgress from "/imports/ui/components/admin-view/admin-schoolwide-progress";
import ScrollIndicator from "/imports/ui/components/scrollIndicator";
import ScrollIndicatorView from "/imports/ui/components/scrollIndicatorView";
import SchoolOverviewDetails from "/imports/ui/components/admin-view/school-overview-details";

import { getCurrentSchoolYear, getInActiveSchoolYear, getMeteorUserSync } from "/imports/api/utilities/utilities";
import { getCurrentEnrolledGrade } from "/imports/api/students/utils";
import { SCHOOL_OVERVIEW_TITLE } from "/imports/api/constants";
import { Grades } from "/imports/api/grades/grades";
import { Sites } from "/imports/api/sites/sites";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";

import { AppDataContext } from "/imports/ui/routing/AppDataContext";
import { getMidYearGoalPosition } from "/imports/api/helpers/getMidYearGoalPosition";

class SchoolOverview extends Component {
  static contextType = AppDataContext;

  constructor(props) {
    super(props);

    this.state = {
      schoolDemographics: null,
      screeningInProgress: true,
      screeningHidden: props.screeningHidden || false
    };
  }

  changePrintingStatus = (element, currentStatus) => {
    this.setState({ [element]: currentStatus });
  };

  getSchoolYear = () => {
    return this.props.schoolYear || getCurrentSchoolYear(this.props.user, this.props.orgid);
  };

  componentDidMount() {
    if (this.props.siteId && this.props.orgid) {
      Meteor.call(
        "Students:GetStudentDemographicsAtSite",
        this.props.siteId,
        this.props.orgid,
        this.getSchoolYear(),
        (error, results) => {
          if (error) {
            console.log("error getting demographics");
          } else {
            this.setState({
              schoolDemographics: results
            });
          }
        }
      );
    }
  }

  renderAdminScreeningInProgress() {
    const { isPrinting, siteName, schoolwideProgressData, currentBMPeriod, schoolYear, orgid } = this.props;
    const user = getMeteorUserSync();
    const inActiveSchoolYear = getInActiveSchoolYear(schoolYear, user, orgid);
    return this.state.screeningInProgress && inActiveSchoolYear ? (
      <AdminSchoolwideProgress
        siteName={siteName}
        progressData={schoolwideProgressData}
        currentBMPeriod={currentBMPeriod}
        changePrintingStatus={status => {
          this.changePrintingStatus("screeningHidden", !status);
        }}
        isPrinting={isPrinting}
      />
    ) : null;
  }

  renderHighSchoolOverview = () => {
    return (
      <SchoolOverviewDetails
        orgid={this.props.orgid}
        siteId={this.props.siteId}
        currentBMPeriod={this.props.currentBMPeriod}
        grades={this.props.grades}
        isHighSchool
        isPrinting={this.props.isPrinting}
      />
    );
  };

  renderElementarySchoolOverview = () => {
    return (
      <React.Fragment>
        {this.props.isPrinting && this.state.screeningHidden
          ? null
          : this.props.currentBMPeriod && this.renderAdminScreeningInProgress()}
        <div className={this.props.isPrinting ? null : "conOverviewContent"}>
          <SchoolOverviewDetails
            orgid={this.props.orgid}
            siteId={this.props.siteId}
            currentBMPeriod={this.props.currentBMPeriod}
            grades={this.props.grades}
            isPrinting={this.props.isPrinting}
          />
          <AdminScreeningResults
            targetScoreData={this.props.percentMeetingTargetByGradeAndSeason}
            isPrinting={this.props.isPrinting}
            grade="ALL"
          />
        </div>
      </React.Fragment>
    );
  };

  render() {
    const isHighSchool = get(this.props.site, "isHighSchool");
    return (
      <div>
        <Header
          keyLabel="schoolOverview"
          siteId={this.props.siteId}
          headerTitle={this.props.headerTitle}
          headerStats={this.state.schoolDemographics}
          orgid={this.props.orgid}
          isPrinting={this.props.isPrinting}
          screeningHidden={this.state.screeningHidden}
        />
        {this.props.isPrinting ? (
          <div>{isHighSchool ? this.renderHighSchoolOverview() : this.renderElementarySchoolOverview()}</div>
        ) : (
          <ScrollIndicator
            container={this}
            targetSelector={".conOverviewMain"}
            indicatorComponent={<ScrollIndicatorView />}
            uniqKey={this.props.siteId}
          >
            <div className="conOverviewMain animated fadeIn">
              {isHighSchool ? this.renderHighSchoolOverview() : this.renderElementarySchoolOverview()}
            </div>
          </ScrollIndicator>
        )}
      </div>
    );
  }
}

SchoolOverview.propTypes = {
  percentMeetingTargetByGradeAndSeason: PropTypes.array,
  schoolwideProgressData: PropTypes.object,
  headerTitle: PropTypes.string,
  siteId: PropTypes.string,
  siteName: PropTypes.string,
  orgid: PropTypes.string,
  user: PropTypes.object,
  currentBMPeriod: PropTypes.object,
  site: PropTypes.object,
  grades: PropTypes.array,
  schoolYear: PropTypes.number,
  isPrinting: PropTypes.bool,
  screeningHidden: PropTypes.bool
};

const SchoolOverviewWithTracker = withTracker(params => {
  const { orgid, siteId, schoolYear, bmPeriods, siteName, currentBMPeriod } = params;
  const site = Sites.findOne({ _id: siteId });
  const headerTitle = SCHOOL_OVERVIEW_TITLE;
  const highSchoolGrades = ["HS"];
  const availableGrades = Grades.find({ _id: { $nin: highSchoolGrades } }, { sort: { sortorder: 1 } }).fetch();
  const grades =
    site && site.isHighSchool
      ? highSchoolGrades
      : [...availableGrades.map(grade => grade.display), ...highSchoolGrades];
  const studentGroups = StudentGroups.find({ siteId }).fetch();
  const completedBenchamarksAndClasswideInterventions = AssessmentResults.find({
    studentGroupId: { $in: studentGroups.map(sg => sg._id) },
    type: { $in: ["benchmark", "classwide"] },
    status: "COMPLETED"
  }).fetch();
  const completedBenchmarks = completedBenchamarksAndClasswideInterventions.filter(ar => ar.type === "benchmark");
  const completedClasswideInterventions = completedBenchamarksAndClasswideInterventions.filter(
    ar => ar.type === "classwide"
  );

  const percentMeetingTargetByGradeAndSeason = compileResultsByGradesInSchool({
    bmPeriods,
    schoolYear,
    grades: availableGrades,
    siteName,
    allAssessmentResults: completedBenchmarks
  });

  const schoolwideProgressData = compileSchoolwideProgressData({
    bmPeriod: currentBMPeriod,
    studentGroups,
    grades: availableGrades,
    completedBenchmarks,
    completedClasswideInterventions
  });

  return {
    percentMeetingTargetByGradeAndSeason,
    schoolwideProgressData,
    headerTitle,
    siteId,
    orgid,
    site,
    grades,
    user: getMeteorUserSync()
  };
})(SchoolOverview);

const SchoolOverviewWithContext = props => {
  const { schoolYear: contextSchoolYear } = useContext(AppDataContext) || {};
  // Use the context schoolYear directly since it should already be resolved
  const schoolYear = contextSchoolYear || props.schoolYear;
  return <SchoolOverviewWithTracker {...props} schoolYear={schoolYear} />;
};

SchoolOverviewWithContext.propTypes = {
  orgid: PropTypes.string,
  schoolYear: PropTypes.number
};

export default SchoolOverviewWithContext;

export function compileResultsByGradesInSchool({ bmPeriods = [], schoolYear, grades, siteName, allAssessmentResults }) {
  const percentMeetingTargetByGradeAndSeason = [];
  let previousSeasonMeetingTarget = null;

  const allAssessmentResultsByBenchmarkPeriodId = groupBy(allAssessmentResults, "benchmarkPeriodId");

  // Compile Results by each season
  bmPeriods.forEach(bmPeriod => {
    const assessmentResultsForBmPeriod = get(allAssessmentResultsByBenchmarkPeriodId, bmPeriod._id, []);
    const seasonalData = {
      name: bmPeriod.name,
      periodId: bmPeriod._id,
      schoolYear,
      data: []
    };
    // Track stats for whole school
    let totalStudentsAssessedOnAllMeasuresForWholeSiteDuringSeason = 0;
    let totalStudentsMeetingAllTargetsForWholeSiteDuringSeason = 0;
    let percentMeetingTargetForWholeSiteDuringSeason = null;

    // Loop through each grade and compile scores
    grades.forEach(grade => {
      let totalStudentsAssessedOnAllMeasuresForThisGradeAndSeason = 0;
      let totalStudentsMeetingAllTargetsForThisGradeAndSeason = 0;
      let percentMeetingTargetForThisGradeAndSeason = null;
      // Get all screening results for this grade and loop through them
      const gradeLevelAssessmentResultsBySeason = assessmentResultsForBmPeriod.filter(
        results => results.grade === grade._id
      );

      gradeLevelAssessmentResultsBySeason.forEach(seasonalGradeLevelResults => {
        // Add the number of students assessed and
        // the number of students meeting target to the totals
        if (
          seasonalGradeLevelResults &&
          seasonalGradeLevelResults.classwideResults &&
          seasonalGradeLevelResults.classwideResults.totalStudentsAssessedOnAllMeasures
        ) {
          // Add on to schoolwide totals
          totalStudentsAssessedOnAllMeasuresForWholeSiteDuringSeason +=
            seasonalGradeLevelResults.classwideResults.totalStudentsAssessedOnAllMeasures;
          totalStudentsMeetingAllTargetsForWholeSiteDuringSeason +=
            seasonalGradeLevelResults.classwideResults.totalStudentsMeetingAllTargets;

          // Add totals for grade
          totalStudentsAssessedOnAllMeasuresForThisGradeAndSeason +=
            seasonalGradeLevelResults.classwideResults.totalStudentsAssessedOnAllMeasures;
          totalStudentsMeetingAllTargetsForThisGradeAndSeason +=
            seasonalGradeLevelResults.classwideResults.totalStudentsMeetingAllTargets;
        }
      });
      // Calculate the percent meeting target
      if (totalStudentsAssessedOnAllMeasuresForThisGradeAndSeason > 0) {
        percentMeetingTargetForThisGradeAndSeason =
          (totalStudentsMeetingAllTargetsForThisGradeAndSeason /
            totalStudentsAssessedOnAllMeasuresForThisGradeAndSeason) *
          100;
      }
      seasonalData.data.push({
        name: getCurrentEnrolledGrade(grade._id),
        y: percentMeetingTargetForThisGradeAndSeason,
        numberOfStudentsAssessed: totalStudentsAssessedOnAllMeasuresForThisGradeAndSeason
      });
    });

    // Calculate Summary Data for School
    let seasonalChange = null;
    if (totalStudentsAssessedOnAllMeasuresForWholeSiteDuringSeason > 0) {
      // Percent of students meeting target
      percentMeetingTargetForWholeSiteDuringSeason = Math.round(
        (totalStudentsMeetingAllTargetsForWholeSiteDuringSeason /
          totalStudentsAssessedOnAllMeasuresForWholeSiteDuringSeason) *
          100
      );

      // Compare value to previous season
      if (previousSeasonMeetingTarget) {
        seasonalChange = percentMeetingTargetForWholeSiteDuringSeason - previousSeasonMeetingTarget;
      }
      // Reassign previousSeason data for next iteration
      previousSeasonMeetingTarget = percentMeetingTargetForWholeSiteDuringSeason;
    }
    seasonalData.summaryData = {
      name: siteName,
      y: percentMeetingTargetForWholeSiteDuringSeason,
      numberOfStudentsAssessed: totalStudentsAssessedOnAllMeasuresForWholeSiteDuringSeason,
      seasonalChange
    };
    percentMeetingTargetByGradeAndSeason.push(seasonalData);
  });

  return percentMeetingTargetByGradeAndSeason;
}

function compileSchoolwideProgressData({
  bmPeriod,
  studentGroups,
  grades,
  completedBenchmarks,
  completedClasswideInterventions
}) {
  const screeningResultsForThisPeriod = completedBenchmarks.filter(ar => ar.benchmarkPeriodId === bmPeriod._id);
  const passedClasswideInterventionsByGradeAndStudentGroupId = completedClasswideInterventions.reduce((acc, cwi) => {
    if (!acc[cwi.grade]) {
      acc[cwi.grade] = {};
    }
    if (!acc[cwi.grade][cwi.studentGroupId]) {
      acc[cwi.grade][cwi.studentGroupId] = [];
    }
    if (cwi.ruleResults?.passed) {
      acc[cwi.grade][cwi.studentGroupId].push(cwi);
    }
    return acc;
  }, {});

  const results = {
    numTotalClasses: 0,
    numTotalClassesWithClasswide: 0,
    numTotalScreened: screeningResultsForThisPeriod.length,
    numTotalReached: 0,
    sitewideComplete: 0,
    sitewideReached: 0,
    gradesScreening: [],
    gradesClasswideProgress: []
  };
  grades.forEach(grade => {
    // Calculate Total Percent Screened for Each Grade
    const groupsInThisGrade = studentGroups.filter(sg => sg.grade === grade._id);
    results.numTotalClasses += groupsInThisGrade.length;
    const screeningsInThisGrade = screeningResultsForThisPeriod.filter(sr => sr.grade === grade._id);
    const midYearGoalPosition = getMidYearGoalPosition(grade._id);
    const reachedGoalsInThisGrade = [];
    let numberOfGroupsInThisGradeWithClasswide = 0;
    groupsInThisGrade.forEach(group => {
      if (
        !passedClasswideInterventionsByGradeAndStudentGroupId?.[group.grade]?.[group._id] &&
        !group.currentClasswideSkill?.assessmentId
      ) {
        return;
      }
      numberOfGroupsInThisGradeWithClasswide += 1;
      const passedClasswideInterventionsForGroup =
        passedClasswideInterventionsByGradeAndStudentGroupId?.[group.grade]?.[group._id] ?? [];
      if (passedClasswideInterventionsForGroup.length >= midYearGoalPosition) {
        reachedGoalsInThisGrade.push(group._id);
      }
    });
    results.numTotalClassesWithClasswide += numberOfGroupsInThisGradeWithClasswide;
    let percentScreenedInThisGrade = 0;
    let percentReachedGoalInThisGrade = null;
    if (groupsInThisGrade?.length) {
      if (screeningsInThisGrade?.length) {
        percentScreenedInThisGrade = Math.round((screeningsInThisGrade.length / groupsInThisGrade.length) * 100);
      }
      if (numberOfGroupsInThisGradeWithClasswide > 0) {
        percentReachedGoalInThisGrade = Math.round(
          (reachedGoalsInThisGrade.length / numberOfGroupsInThisGradeWithClasswide) * 100
        );
        results.numTotalReached += reachedGoalsInThisGrade.length;
      }
    }
    results.gradesScreening.push({
      gradeId: grade._id,
      grade: grade.display,
      percentComplete: percentScreenedInThisGrade
    });

    results.gradesClasswideProgress.push({
      gradeId: grade._id,
      grade: grade.display,
      percentComplete: percentReachedGoalInThisGrade
    });
  });

  // Get schoolwide percent
  if (results.numTotalClasses) {
    results.sitewideComplete = Math.round((screeningResultsForThisPeriod.length / results.numTotalClasses) * 100);
  }
  if (results.numTotalClassesWithClasswide) {
    results.sitewideReached = Math.round((results.numTotalReached / results.numTotalClassesWithClasswide) * 100);
  }

  return results;
}
